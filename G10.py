import random

def solicitar_entero(mensaje: str) -> int:
    while True:
        try:
            v = int(input(mensaje))
            if v <= 0:
                print("El valor debe ser un entero positivo.")
                continue
            return v
        except ValueError:
            print("Introduce un número entero válido.")

def ejecutar_simulacion_volados() -> None:
    print("SIMULADOR DE VOLADOS")
    print("=" * 55)

    dinero_inicial   = solicitar_entero(" Cantidad inicial de dinero: $")
    apuesta_inicial  = solicitar_entero(" Apuesta base: $")

    while True:
        objetivo_meta = solicitar_entero(" Meta a alcanzar: $")
        if objetivo_meta > dinero_inicial:
            break
        print("La meta debe ser mayor que el dinero inicial.")

    corridas_deseadas = solicitar_entero(" ¿Cuántas corridas completas deseas realizar?: ")

    print(f"\n{'='*70}")
    print("CONFIGURACIÓN DE LA SIMULACIÓN:")
    print(f"• Dinero inicial : ${dinero_inicial}")
    print(f"• Apuesta base   : ${apuesta_inicial}")
    print(f"• Meta objetivo  : ${objetivo_meta}")
    print(f"• Corridas a realizar : {corridas_deseadas}")
    print(f"{'='*70}")

    victorias_totales = derrotas_totales = 0
    total_volados = ganadores = 0

    for numero_corrida in range(1, corridas_deseadas + 1):
        print(f"\n INICIANDO CORRIDA #{numero_corrida}")
        print("─" * 50)

        dinero_actual  = dinero_inicial
        apuesta_actual = apuesta_inicial
        volado_numero  = 1

        while 0 < dinero_actual < objetivo_meta:
            r = random.random()           # 0 ≤ r < 1
            total_volados += 1
            if r > 0.5:
                ganadores += 1

            dinero_previo      = dinero_actual
            apuesta_realizada  = min(apuesta_actual, dinero_actual)
            gano_volado        = r > 0.5

            if gano_volado:
                dinero_actual += apuesta_realizada
                estado_volado  = " GANÓ"
                apuesta_actual = apuesta_inicial
            else:
                dinero_actual -= apuesta_realizada
                estado_volado  = " PERDIÓ"
                apuesta_actual = max(1, min(apuesta_actual * 2, dinero_actual))

            estado_corrida = (
                " META ALCANZADA" if dinero_actual >= objetivo_meta
                else " QUIEBRA"    if dinero_actual <= 0
                else " Continúa..."
            )

            print(f"Volado {volado_numero:2d}: ${dinero_previo:9,d} "
                  f"→ Apuesta ${apuesta_realizada:7,d} → R={r:.6f} →"
                  f"{estado_volado} → ${dinero_actual:9,d} [{estado_corrida}]")

            volado_numero += 1

        if dinero_actual >= objetivo_meta:
            victorias_totales += 1
            resultado_corrida = " VICTORIA"
        else:
            derrotas_totales += 1
            resultado_corrida = " DERROTA"

        print(f"📊 RESULTADO CORRIDA #{numero_corrida}: {resultado_corrida}")
        print(f"   • Volados jugados: {volado_numero - 1}")
        print(f"   • Dinero final   : ${dinero_actual}")

    mostrar_estadisticas_finales(
        corridas_deseadas,
        victorias_totales,
        derrotas_totales,
        total_volados,
        ganadores
    )

def mostrar_estadisticas_finales(
        total_corridas: int,
        victorias: int,
        derrotas: int,
        total_volados: int,
        ganadores: int
) -> None:
    print(f"\n{' ESTADÍSTICAS FINALES ':^70}")
    print("=" * 70)

    print(" RESUMEN GENERAL:")
    print(f"   • Total de corridas : {total_corridas}")
    print(f"   • Victorias         : {victorias}")
    print(f"   • Derrotas          : {derrotas}")

    if total_corridas:
        print("\n PROBABILIDADES:")
        print(f"   • Probabilidad de victoria : {(victorias/total_corridas)*100:6.3f}%")
        print(f"   • Probabilidad de derrota  : {(derrotas/total_corridas)*100:6.3f}%")

    if total_volados:
        print("\n NÚMEROS ALEATORIOS:")
        print(f"   • Total de volados             : {total_volados}")
        print(f"   • Promedio de volados/corrida  : {total_volados/total_corridas:6.2f}")
        print(f"   • % de números ganadores (>0.5): {(ganadores/total_volados)*100:6.2f}%")

    print("=" * 70)

def main() -> None:
    try:
        ejecutar_simulacion_volados()
    except KeyboardInterrupt:
        print("\n Simulación interrumpida por el usuario.")

    input("\n Presiona Enter para salir...")

if __name__ == "__main__":
    main()
