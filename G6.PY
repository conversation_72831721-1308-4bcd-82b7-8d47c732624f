from scipy.stats import chi2
from random import random
from functools import reduce

def solicitar_parametros():
    N = int(input('Ingrese cantidad de números rectangulares: '))
    n = int(input('\nIngrese cantidad de subintervalos: '))
    
    while True:
        alpha = float(input('\nIngresa nivel de significancia (entre 0 y 1. Sea 1 = 100%): '))
        if 0 < alpha < 1:
            return N, n, alpha
        print("✗ El valor debe estar entre 0 y 1")

def crear_numeros_aleatorios(cantidad):
    return [round(random(), 5) for _ in range(cantidad)]

def definir_intervalos(num_intervalos):
    ancho = 1.0 / num_intervalos
    return [round((i + 1) * ancho, 5) for i in range(num_intervalos)]

def clasificar_numeros_por_intervalo(numeros, limites_superiores):
    frecuencias = [0] * len(limites_superiores)
    for numero in numeros:
        for i, limite in enumerate(limites_superiores):
            if numero <= limite:
                frecuencias[i] += 1
                break
    return frecuencias

def calcular_frecuencia_esperada(total_numeros, num_intervalos):
    return total_numeros / num_intervalos

def aplicar_formula_chi_cuadrado(frecuencias_observadas, frecuencia_esperada):
    diferencias_cuadradas = [(fo - frecuencia_esperada)**2 for fo in frecuencias_observadas]
    suma_total = sum(diferencias_cuadradas)
    return suma_total / frecuencia_esperada

def obtener_valor_critico_chi2(alpha, grados_libertad):
    return chi2.ppf(1 - alpha, grados_libertad)

def construir_tabla_resultados(limites_superiores, frecuencia_esperada, frecuencias_observadas):
    print('\n' + '-'*50)
    print(f'| {"Intervalo":^15} | {"FEi":^5} | {"FOi":^5} |')
    print('-'*50)
    
    limite_anterior = 0
    for i, limite_actual in enumerate(limites_superiores):
        intervalo = f'{limite_anterior:.5f} - {limite_actual:.5f}'
        print(f'| {intervalo:^15} | {int(frecuencia_esperada):^5} | {frecuencias_observadas[i]:^5} |')
        limite_anterior = limite_actual
    
    print('-'*50)

def mostrar_calculo_detallado(frecuencias_observadas, frecuencia_esperada, estadistico):
    diferencias_cuadradas = [(fo - frecuencia_esperada)**2 for fo in frecuencias_observadas]
    suma_diferencias = sum(diferencias_cuadradas)
    
    print(f'\nFórmula: X² = Σ((FOi - FEi)² / FEi)')
    print(f'X² = (1 / {frecuencia_esperada}) * {round(suma_diferencias, 4)}')
    print(f'Estadístico calculado: {round(estadistico, 5)}')

def evaluar_y_mostrar_conclusion(estadistico, valor_critico, alpha, grados_libertad):
    print(f'Valor crítico (α={alpha} y gl={grados_libertad}): {round(valor_critico, 5)}')
    print(f'{round(estadistico, 5)} < {round(valor_critico, 5)}')
    
    if estadistico < valor_critico:
        print('✓ Los números son aceptados (siguen distribución uniforme)')
        return True
    else:
        print('✗ Los números son rechazados (no siguen distribución uniforme)')
        return False

def ejecutar_prueba_bondad_ajuste():
    N, n, alpha = solicitar_parametros()
    
    numeros = crear_numeros_aleatorios(N)
    print(f'\nNúmeros generados: {numeros}')
    
    limites_intervalos = definir_intervalos(n)
    frecuencia_esperada = calcular_frecuencia_esperada(N, n)
    grados_libertad = n - 1
    
    frecuencias_observadas = clasificar_numeros_por_intervalo(numeros, limites_intervalos)
    estadistico_chi2 = aplicar_formula_chi_cuadrado(frecuencias_observadas, frecuencia_esperada)
    valor_critico = obtener_valor_critico_chi2(alpha, grados_libertad)
    
    construir_tabla_resultados(limites_intervalos, frecuencia_esperada, frecuencias_observadas)
    mostrar_calculo_detallado(frecuencias_observadas, frecuencia_esperada, estadistico_chi2)
    evaluar_y_mostrar_conclusion(estadistico_chi2, valor_critico, alpha, grados_libertad)
    
    input("\nPresiona Enter para cerrar...")

def prueba_chi2_funcional_pura(numeros, n, alpha):
    intervalos = list(map(lambda i: (i + 1) / n, range(n)))
    clasificar = lambda num: next(i for i, limite in enumerate(intervalos) if num <= limite)
    
    frecuencias = [0] * n
    for num in numeros:
        frecuencias[clasificar(num)] += 1
    
    fe = len(numeros) / n
    chi2_calc = reduce(lambda acc, fo: acc + ((fo - fe) ** 2), frecuencias, 0) / fe
    
    return chi2_calc, chi2.ppf(1 - alpha, n - 1)

if __name__ == "__main__":
    ejecutar_prueba_bondad_ajuste()
