import numpy as np

def generar_uniformes(n, a, b):
    r_vals = np.round(np.random.uniform(0, 1, n), 5)
    x_vals = np.round(a + (b - a) * r_vals, 5)
    return x_vals, r_vals

if __name__ == "__main__":
    cantidad = int(input("¿Cuántos números deseas generar? "))
    a = float(input("Ingresa el límite inferior del intervalo (a): "))
    b = float(input("Ingresa el límite superior del intervalo (b): "))

    if b <= a:
        print(" Error: El límite superior (b) debe ser mayor que el inferior (a).")
    else:
        x_vals, r_vals = generar_uniformes(cantidad, a, b)
        print(f"\nValores R generados: {r_vals}")
        print(f"Valores X generados (uniformes entre {a} y {b}): {x_vals}")
